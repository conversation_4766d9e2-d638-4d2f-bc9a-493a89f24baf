#!/usr/bin/env Rscript

# =============================================================================
# Automated DID Analysis Script
# Purpose: Run 6 DID models for 3 different limits (180, 270, 450)
# =============================================================================

# Load required libraries
suppressPackageStartupMessages({
  library(stats)
  library(lme4)
  library(readr)
  library(ggplot2)
  library(stargazer)
  library(lmtest)
  library(MuMIn)
  library(lmerTest)
  library(survival)
  library(ggpubr)
  library(survminer)
  library(car)
  library(coxme)
  library(dplyr)
  library(broom)
  library(broom.mixed)
  library(tidyr)
  library(parallel)
  library(foreach)
  library(doParallel)
})

# Configuration
limits <- c(180, 270, 365, 450)
base_result_dir <- "/home/<USER>/repo/disengagement/result/20250731_did_result_psm_local_matching"

# Data file paths
data_files <- list(
  "180" = "/home/<USER>/repo/disengagement/result/20250731_did_result_psm_local_matching/compiled_data_test_limit180_processed.csv",
  "270" = "/home/<USER>/repo/disengagement/result/20250731_did_result_psm_local_matching/compiled_data_test_limit270_processed.csv", 
  "450" = "/home/<USER>/repo/disengagement/result/20250731_did_result_psm_local_matching/compiled_data_test_limit450_processed.csv",
  "365" = "/home/<USER>/repo/disengagement/result/20250731_did_result_psm_local_matching/compiled_data_test_limit365_processed.csv"
)

# Set up control parameters for lmer
ctrl <- lmerControl(
  optimizer = "nloptwrap",
  optCtrl = list(maxeval = 1e5, xtol_abs = 1e-8, ftol_abs = 1e-8),
  calc.derivs = FALSE
) 

# Helper functions
create_dir <- function(dir_path) {
  if (!dir.exists(dir_path)) {
    dir.create(dir_path, recursive = TRUE, showWarnings = FALSE)
    cat("Created directory:", dir_path, "\n")
  }
}

# Function to check if txt results already exist
results_exist <- function(model_name, limit, output_dir) {
  text_file <- file.path(output_dir, paste0(model_name, "_detailed.txt"))
  return(file.exists(text_file))
}

# Enhanced function to save model results - only txt files
save_model_results_enhanced <- function(model, model_name, limit, output_dir) {
  cat("📝 Saving txt results for:", model_name, "\n")

  # Save detailed text results only
  text_file <- file.path(output_dir, paste0(model_name, "_detailed.txt"))
  
  tryCatch({
    # Capture all output
    output_text <- capture.output({
      cat("=== DID Model Results:", model_name, "=== (Limit:", limit, ")\n\n")
      cat("Analysis Date:", Sys.time(), "\n\n")
      
      # Model formula
      cat("Model Formula:\n")
      cat(as.character(formula(model)), "\n\n")
      
      # Model convergence info
      cat("Model Convergence:\n")
      if (is.null(model@optinfo$conv$lme4$messages)) {
        cat("✓ Model converged successfully\n")
      } else {
        cat("⚠️  Model convergence warnings:\n")
        cat(model@optinfo$conv$lme4$messages, "\n")
      }
      cat("\n")
      
      # Model summary using capture.output
      cat("Model Summary:\n")
      print(summary(model))
      cat("\n")
      
      # VIF values
      cat("Variance Inflation Factors (VIF):\n")
      tryCatch({
        vif_values <- vif(model)
        print(vif_values)
      }, error = function(e) {
        cat("VIF calculation failed:", e$message, "\n")
      })
      cat("\n")
      
      # R-squared values
      cat("R-squared Values:\n")
      tryCatch({
        r2_values <- r.squaredGLMM(model)
        print(r2_values)
      }, error = function(e) {
        cat("R-squared calculation failed:", e$message, "\n")
      })
      cat("\n")
      
      # Model diagnostics
      cat("Model Diagnostics:\n")
      cat("AIC:", AIC(model), "\n")
      cat("BIC:", BIC(model), "\n")
      cat("Log-likelihood:", logLik(model), "\n")
      cat("\n")
      
      # Random effects summary
      cat("Random Effects Summary:\n")
      print(VarCorr(model))
      cat("\n")
      
      cat("=== End of Detailed Results ===\n")
    })
    
    # Write to file using sink
    sink(text_file)
    cat(paste(output_text, collapse = "\n"))
    sink()
    
    cat("✓ Detailed results saved:", text_file, "(Size:", file.size(text_file), "bytes)\n")
    
  }, error = function(e) {
    sink()  # Close sink in case of error
    cat("❌ Error saving detailed results:", e$message, "\n")
  })
  
  # # 3. Save tidy results using broom
  # tidy_file <- file.path(output_dir, paste0(model_name, "_tidy.csv"))
  # tryCatch({
  #   # Fixed effects
  #   fixed_effects <- tidy(model, conf.int = TRUE, conf.level = 0.95)
  #   write.csv(fixed_effects, tidy_file, row.names = FALSE)
  #   cat("✓ Tidy fixed effects saved:", tidy_file, "\n")
    
  #   # Random effects
  #   random_effects <- tidy(model, effects = "ran_pars")
  #   random_file <- file.path(output_dir, paste0(model_name, "_random_effects.csv"))
  #   write.csv(random_effects, random_file, row.names = FALSE)
  #   cat("✓ Random effects saved:", random_file, "\n")
    
  #   # Model performance metrics
  #   performance <- glance(model)
  #   performance_file <- file.path(output_dir, paste0(model_name, "_performance.csv"))
  #   write.csv(performance, performance_file, row.names = FALSE)
  #   cat("✓ Performance metrics saved:", performance_file, "\n")
    
  # }, error = function(e) {
  #   cat("❌ Error saving tidy results:", e$message, "\n")
  # })
  
  #    # 4. Save stargazer table (with better error handling for mixed models)
  #  stargazer_file <- file.path(output_dir, paste0(model_name, "_stargazer.txt"))
  #  tryCatch({
  #    sink(stargazer_file)
  #    # For mixed models, we need to extract fixed effects for stargazer
  #    fixed_effects <- fixef(model)
  #    stargazer(as.data.frame(t(fixed_effects)), type = "text", 
  #              title = paste("DID Model Results:", model_name, "(Limit:", limit, ")"),
  #              column.labels = c("Fixed Effects"),
  #              dep.var.labels = "Dependent Variable",
  #              notes = paste("Analysis date:", Sys.time(), "\nNote: Only fixed effects shown"))
  #    sink()
  #    cat("✓ Stargazer table saved:", stargazer_file, "\n")
  #  }, error = function(e) {
  #    sink()  # Close sink in case of error
  #    cat("⚠️  Stargazer table not saved (mixed model limitation):", e$message, "\n")
  #  })
}

preprocess_data <- function(data, limit) {
  cat("Preprocessing data for limit:", limit, "\n")
  
  # Standardize continuous variables
  data <- data %>%
    mutate(
      log_tenure_c = scale(log_tenure),
      log_commit_percent_c = scale(log_commit_percent),
      log_commits_c = scale(log_commits),
      log_project_commits = scale(log_project_commits),
      log_project_contributors = scale(log_project_contributors),
      log_project_age = scale(log_project_age),
      log_project_commits_before_treatment = scale(log_project_commits_before_treatment),
      log_project_contributors_before_treatment = scale(log_project_contributors_before_treatment),
      log_project_age_before_treatment = scale(log_project_age_before_treatment),
      log_newcomers = scale(log_newcomers)
    )
  
  # Filter growth phases
  # data <- data[!is.na(data$growth_phase) & data$growth_phase != '', ]
  # data <- data[data$growth_phase %in% c('accelerating', 'decelerating', 'first 3 months', 'saturation', 'steady'), ]
  
  # Set factor levels and contrasts
  data$project_main_language <- factor(data$project_main_language)
  # data$growth_phase <- factor(data$growth_phase)
  data$project_main_language <- relevel(data$project_main_language, ref = "JavaScript")
  # data$growth_phase <- relevel(data$growth_phase, ref = "steady")
  contrasts(data$project_main_language) <- "contr.sum"
  # contrasts(data$growth_phase) <- "contr.sum"
  
  cat("✓ Data preprocessing completed. Observations:", nrow(data), "\n")
  return(data)
}

# Preprocessing function for other dependent variables (with drop_na)
preprocess_data_with_dropna <- function(data, limit) {
  cat("Preprocessing data with drop_na for limit:", limit, "\n")
  
  # Standardize continuous variables
  data <- data %>%
    mutate(
      log_tenure_c = scale(log_tenure),
      log_commit_percent_c = scale(log_commit_percent),
      log_commits_c = scale(log_commits),
      log_project_commits = scale(log_project_commits),
      log_project_contributors = scale(log_project_contributors),
      log_project_age = scale(log_project_age),
      log_project_commits_before_treatment = scale(log_project_commits_before_treatment),
      log_project_contributors_before_treatment = scale(log_project_contributors_before_treatment),
      log_project_age_before_treatment = scale(log_project_age_before_treatment),
      log_newcomers = scale(log_newcomers)
    ) %>%
    # 移除含有缺失值的观测（确保数据清洁）
    tidyr::drop_na()
  
  # Filter growth phases
  # data <- data[!is.na(data$growth_phase) & data$growth_phase != '', ]
  # data <- data[data$growth_phase %in% c('accelerating', 'decelerating', 'first 3 months', 'saturation', 'steady'), ]
  
  # Set factor levels and contrasts
  data$project_main_language <- factor(data$project_main_language)
  # data$growth_phase <- factor(data$growth_phase)
  data$project_main_language <- relevel(data$project_main_language, ref = "JavaScript")
  # data$growth_phase <- relevel(data$growth_phase, ref = "steady")
  contrasts(data$project_main_language) <- "contr.sum"
  # contrasts(data$growth_phase) <- "contr.sum"
  
  cat("✓ Data preprocessing with drop_na completed. Observations:", nrow(data), "\n")
  return(data)
}

# Main treatment effect models - PR Throughput only
run_pr_throughput_model <- function(data, limit, output_dir) {
  cat("\n--- Running PR Throughput Model ---\n")
  
  cat("1. PR Throughput model...\n")
  model_name <- "did_main_pr_throughput"

  if (results_exist(model_name, limit, output_dir)) {
    cat("📂 Results already exist for:", model_name, "\n")
    return()
  }
  
  tryCatch({
    model <- lmer(
      log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +
        log_project_commits + log_project_contributors + log_project_age + 
        (1 | time_cohort_effect) + (1 | repo_cohort_effect),
      REML = FALSE, data = data, control = ctrl
    )
    cat("✓ PR Throughput model fitted successfully\n")
    save_model_results_enhanced(model, model_name, limit, output_dir)
  }, error = function(e) {
    cat("❌ Error fitting PR Throughput model:", e$message, "\n")
  })
}

# Main treatment effect models - PR Accept Rate and Time to Merge
run_remaining_main_models <- function(data, limit, output_dir) {
  cat("\n--- Running Remaining Main Treatment Effect Models ---\n")
  
  # 2. PR Accept Rate
  cat("2. PR Accept Rate model...\n")
  model_name <- "did_main_pr_accept_rate"

  if (results_exist(model_name, limit, output_dir)) {
    cat("📂 Results already exist for:", model_name, "\n")
  } else {
    tryCatch({
      model <- lmer(
        log_pull_request_success_rate ~ is_post_treatment + is_treated + is_treated:is_post_treatment +
          log_project_commits + log_project_contributors + log_project_age + 
          (1 | time_cohort_effect) + (1 | repo_cohort_effect),
        REML = FALSE, data = data, control = ctrl
      )
      cat("✓ PR Accept Rate model fitted successfully\n")
      save_model_results_enhanced(model, model_name, limit, output_dir)
    }, error = function(e) {
      cat("❌ Error fitting PR Accept Rate model:", e$message, "\n")
    })
  }
  
  # 3. PR Time to Merge
  cat("3. PR Time to Merge model...\n")
  model_name <- "did_main_pr_time_to_merge"

  if (results_exist(model_name, limit, output_dir)) {
    cat("📂 Results already exist for:", model_name, "\n")
  } else {
    tryCatch({
      model <- lmer(
        log_time_to_merge ~ is_post_treatment + is_treated + is_treated:is_post_treatment +
          log_project_commits + log_project_contributors + log_project_age + 
          (1 | time_cohort_effect) + (1 | repo_cohort_effect),
        REML = FALSE, data = data, control = ctrl
      )
      cat("✓ PR Time to Merge model fitted successfully\n")
      save_model_results_enhanced(model, model_name, limit, output_dir)
    }, error = function(e) {
      cat("❌ Error fitting PR Time to Merge model:", e$message, "\n")
    })
  }
}

# Moderating effect models
run_moderating_models <- function(data_pr_throughput, data_others, limit, output_dir) {
  cat("\n--- Running Moderating Effect Models ---\n")
  
  # 4. Moderating PR Throughput (uses data without drop_na)
  cat("4. Moderating PR Throughput model...\n")
  model_name <- "did_moderating_pr_throughput"

  if (results_exist(model_name, limit, output_dir)) {
    cat("📂 Results already exist for:", model_name, "\n")
  } else {
    tryCatch({
      model <- lmer(
        log_pr_throughput ~ is_post_treatment + is_treated + 
          is_post_treatment:is_treated:log_tenure_c +
          is_post_treatment:is_treated:log_commit_percent_c +
          is_post_treatment:is_treated:log_commits_c +
          is_post_treatment:is_treated:log_newcomers +
          is_post_treatment:is_treated:log_project_commits_before_treatment +
          is_post_treatment:is_treated:log_project_contributors_before_treatment +
          is_post_treatment:is_treated:log_project_age_before_treatment +
          # is_post_treatment:is_treated:growth_phase +
          is_post_treatment:is_treated:project_main_language +
          log_project_commits + log_project_contributors + log_project_age +
          (1 | time_cohort_effect) + (1 | repo_cohort_effect),
        REML = FALSE, data = data_pr_throughput, control = ctrl
      )
      cat("✓ Moderating PR Throughput model fitted successfully\n")
      save_model_results_enhanced(model, model_name, limit, output_dir)
    }, error = function(e) {
      cat("❌ Error fitting Moderating PR Throughput model:", e$message, "\n")
    })
  }
  
  # 5. Moderating PR Accept Rate (uses data with drop_na)
  cat("5. Moderating PR Accept Rate model...\n")
  model_name <- "did_moderating_pr_accept_rate"

  if (results_exist(model_name, limit, output_dir)) {
    cat("📂 Results already exist for:", model_name, "\n")
  } else {
    tryCatch({
      model <- lmer(
        log_pull_request_success_rate ~is_post_treatment + is_treated + 
          is_post_treatment:is_treated:log_tenure_c +
          is_post_treatment:is_treated:log_commit_percent_c +
          is_post_treatment:is_treated:log_commits_c +
          is_post_treatment:is_treated:log_newcomers +
          is_post_treatment:is_treated:log_project_commits_before_treatment +
          is_post_treatment:is_treated:log_project_contributors_before_treatment +
          is_post_treatment:is_treated:log_project_age_before_treatment +
          # is_post_treatment:is_treated:growth_phase +
          is_post_treatment:is_treated:project_main_language +
          log_project_commits + log_project_contributors + log_project_age +
          (1 | time_cohort_effect) + (1 | repo_cohort_effect),
        REML = FALSE, data = data_others, control = ctrl
      )
      cat("✓ Moderating PR Accept Rate model fitted successfully\n")
      save_model_results_enhanced(model, model_name, limit, output_dir)
    }, error = function(e) {
      cat("❌ Error fitting Moderating PR Accept Rate model:", e$message, "\n")
    })
  }
  
  # 6. Moderating PR Time to Merge (uses data with drop_na)
  cat("6. Moderating PR Time to Merge model...\n")
  model_name <- "did_moderating_pr_time_to_merge"

  if (results_exist(model_name, limit, output_dir)) {
    cat("📂 Results already exist for:", model_name, "\n")
  } else {
    tryCatch({
      model <- lmer(
        log_time_to_merge ~ is_post_treatment + is_treated + 
          is_post_treatment:is_treated:log_tenure_c +
          is_post_treatment:is_treated:log_commit_percent_c +
          is_post_treatment:is_treated:log_commits_c +
          is_post_treatment:is_treated:log_newcomers +
          is_post_treatment:is_treated:log_project_commits_before_treatment +
          is_post_treatment:is_treated:log_project_contributors_before_treatment +
          is_post_treatment:is_treated:log_project_age_before_treatment +
          # is_post_treatment:is_treated:growth_phase +
          is_post_treatment:is_treated:project_main_language +
          log_project_commits + log_project_contributors + log_project_age +
          (1 | time_cohort_effect) + (1 | repo_cohort_effect),
        REML = FALSE, data = data_others, control = ctrl
      )
      cat("✓ Moderating PR Time to Merge model fitted successfully\n")
      save_model_results_enhanced(model, model_name, limit, output_dir)
    }, error = function(e) {
      cat("❌ Error fitting Moderating PR Time to Merge model:", e$message, "\n")
    })
  }
}

# Function to process a single limit
process_single_limit <- function(limit) {
  start_time <- Sys.time()
  cat("\n", paste(rep("=", 60), collapse=""), "\n")
  cat("🚀 [LIMIT", limit, "] Starting processing (PID:", Sys.getpid(), ") at", format(start_time, "%H:%M:%S"), "\n")
  cat(paste(rep("=", 60), collapse=""), "\n")

  # Create output directory
  cat("📁 [LIMIT", limit, "] Creating output directory...\n")
  output_dir <- file.path(base_result_dir, paste0("attrition_", limit))
  create_dir(output_dir)
  cat("✅ [LIMIT", limit, "] Output directory ready:", output_dir, "\n")

  # Load data
  cat("📂 [LIMIT", limit, "] Loading data file...\n")
  data_file <- data_files[[as.character(limit)]]
  if (!file.exists(data_file)) {
    cat("❌ [LIMIT", limit, "] ERROR: Data file not found:", data_file, "\n")
    cat("⏭️  [LIMIT", limit, "] Skipping this limit\n")
    return(list(limit = limit, status = "skipped", reason = "data file not found"))
  }

  cat("� [LIMIT", limit, "] Reading CSV from:", basename(data_file), "\n")
  compiled_data_test <- read.csv(data_file)
  cat("✅ [LIMIT", limit, "] Data loaded successfully! Initial observations:", nrow(compiled_data_test), "\n")

  # Preprocess data for PR Throughput (without drop_na)
  cat("🔄 [LIMIT", limit, "] Step 1/3: Preprocessing data for PR Throughput (without drop_na)...\n")
  compiled_data_test_pr_throughput <- preprocess_data(compiled_data_test, limit)
  cat("✅ [LIMIT", limit, "] PR Throughput data preprocessing completed\n")

  # Preprocess data for other dependent variables (with drop_na)
  cat("🔄 [LIMIT", limit, "] Step 2/3: Preprocessing data for other models (with drop_na)...\n")
  compiled_data_test_others <- preprocess_data_with_dropna(compiled_data_test, limit)
  cat("✅ [LIMIT", limit, "] Other models data preprocessing completed\n")

  results <- list(limit = limit, status = "completed", errors = c())

  # Run PR Throughput model (without drop_na)
  tryCatch({
    run_pr_throughput_model(compiled_data_test_pr_throughput, limit, output_dir)
    cat("\n✅ Completed PR Throughput analysis for limit:", limit, "\n")
  }, error = function(e) {
    error_msg <- paste("PR Throughput error:", e$message)
    cat("\n❌", error_msg, "\n")
    results$errors <<- c(results$errors, error_msg)
  })

  # Run remaining main models (with drop_na)
  tryCatch({
    run_remaining_main_models(compiled_data_test_others, limit, output_dir)
    cat("\n✅ Completed remaining main models for limit:", limit, "\n")
  }, error = function(e) {
    error_msg <- paste("Remaining main models error:", e$message)
    cat("\n❌", error_msg, "\n")
    results$errors <<- c(results$errors, error_msg)
  })

  # Run moderating models (PR Throughput uses data without drop_na, others use data with drop_na)
  # tryCatch({
  #   run_moderating_models(compiled_data_test_pr_throughput, compiled_data_test_others, limit, output_dir)
  #   cat("\n✅ Completed moderating models for limit:", limit, "\n")
  # }, error = function(e) {
  #   error_msg <- paste("Moderating models error:", e$message)
  #   cat("\n❌", error_msg, "\n")
  #   results$errors <<- c(results$errors, error_msg)
  # })

  cat("\n✅ Completed processing for limit:", limit, "\n")

  if (length(results$errors) > 0) {
    results$status <- "completed_with_errors"
  }

  return(results)
}

# Main execution with parallel processing
cat("Starting Automated DID Analysis with Parallel Processing\n")
cat(paste(rep("=", 50), collapse=""), "\n")

# Detect number of cores and set up parallel backend
n_cores <- min(detectCores() - 1, length(limits))  # Leave one core free, or use number of limits if fewer
cat("🖥️  Detected", detectCores(), "cores, using", n_cores, "cores for parallel processing\n")

# Register parallel backend
cl <- makeCluster(n_cores)
registerDoParallel(cl)

# Export necessary objects to cluster
clusterExport(cl, c("base_result_dir", "data_files", "ctrl",
                   "create_dir", "results_exist", "save_model_results_enhanced",
                   "preprocess_data", "preprocess_data_with_dropna",
                   "run_pr_throughput_model", "run_remaining_main_models", "run_moderating_models"))

# Load required libraries on each worker
clusterEvalQ(cl, {
  suppressPackageStartupMessages({
    library(stats)
    library(lme4)
    library(readr)
    library(ggplot2)
    library(stargazer)
    library(lmtest)
    library(MuMIn)
    library(lmerTest)
    library(survival)
    library(ggpubr)
    library(survminer)
    library(car)
    library(coxme)
    library(dplyr)
    library(broom)
    library(broom.mixed)
    library(tidyr)
  })
})

cat("🚀 Starting parallel processing of", length(limits), "limits...\n")
start_time <- Sys.time()

# Run parallel processing
parallel_results <- parLapply(cl, limits, process_single_limit)

end_time <- Sys.time()
processing_time <- end_time - start_time

# Stop cluster
stopCluster(cl)

cat("\n⏱️  Total processing time:", round(processing_time, 2), attr(processing_time, "units"), "\n")

# Summary
cat("\n", paste(rep("=", 50), collapse=""), "\n")
cat("📋 Parallel Processing Summary:\n")
cat(paste(rep("=", 50), collapse=""), "\n")

# Process results from parallel execution
for (result in parallel_results) {
  limit <- result$limit
  status <- result$status

  cat("\n🔍 Limit", limit, "- Status:", status, "\n")

  if (length(result$errors) > 0) {
    cat("  ⚠️  Errors encountered:\n")
    for (error in result$errors) {
      cat("    -", error, "\n")
    }
  }

  # Check output files
  output_dir <- file.path(base_result_dir, paste0("attrition_", limit))
  if (dir.exists(output_dir)) {
    files <- list.files(output_dir, pattern = "\\.txt$")
    cat("  📁", length(files), "txt files created\n")
    for (file in files) {
      file_size <- file.size(file.path(output_dir, file))
      cat("    📄", file, "(", file_size, "bytes)\n")
    }
  } else {
    cat("  ❌ Output directory not found\n")
  }
}

# Overall summary
successful_limits <- sapply(parallel_results, function(x) x$status %in% c("completed", "completed_with_errors"))
total_successful <- sum(successful_limits)

cat("\n", paste(rep("=", 50), collapse=""), "\n")
cat("� Overall Results:\n")
cat("  ✅ Successfully processed:", total_successful, "out of", length(limits), "limits\n")
cat("  ⏱️  Total processing time:", round(processing_time, 2), attr(processing_time, "units"), "\n")
cat("  🖥️  Used", n_cores, "parallel cores\n")

if (total_successful == length(limits)) {
  cat("\n�🎉 All analyses completed successfully!\n")
} else {
  failed_limits <- limits[!successful_limits]
  cat("\n⚠️  Some analyses failed for limits:", paste(failed_limits, collapse = ", "), "\n")
  cat("🎉 Partial completion - check individual limit results above.\n")
}